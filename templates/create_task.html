{% extends "base.html" %}

{% block title %}Создание задачи{% endblock %}

{% block content %}
<div class="task-form">
    <h1>{{ title }}</h1>
    
    <form method="POST" class="fade-in">
        <div class="form-group">
            <label class="form-label" for="title">Название задачи</label>
            <input type="text" class="form-input" id="title" name="title" required>
        </div>

        <div class="form-group">
            <label class="form-label" for="description">Описание</label>
            <textarea class="form-input" id="description" name="description" rows="4" required></textarea>
        </div>

        {% if current_user.is_admin %}
        <div class="form-group">
            <label class="form-label" for="assignee">Исполнитель</label>
            <select class="form-input" id="assignee" name="assignee_id" required>
                <option value="">Выберите исполнителя</option>
                {% for user in users %}
                    {% if not user.is_admin %}
                        <option value="{{ user.id }}">{{ user.name }}</option>
                    {% endif %}
                {% endfor %}
            </select>
        </div>

        <div class="form-group">
            <label class="form-label" for="task_type">Тип задачи</label>
            <select class="form-input" id="task_type" name="task_type" required>
                <option value="общая">Общая</option>
                <option value="личная">Личная</option>
            </select>
        </div>
        {% endif %}

        <div class="form-group">
            <label class="form-label" for="deadline">Срок выполнения</label>
            <input type="datetime-local" class="form-input" id="deadline" name="deadline" required>
        </div>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary">Создать задачу</button>
            <a href="{{ url_for('index') }}" class="btn btn-secondary">Отмена</a>
        </div>
    </form>
</div>

{% block extra_css %}
<style>
.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.task-form {
    margin-top: 2rem;
}

textarea.form-input {
    resize: vertical;
    min-height: 100px;
}
</style>
{% endblock %}
{% endblock %} 