{% extends "base.html" %}

{% block content %}
<div class="page-header">
    <h1>{{ title }}</h1>
</div>

<div class="task-filters">
    <div class="filter-group">
        <a href="{{ url_for(request.endpoint, status='all') }}" class="btn {% if not status %}btn-primary{% else %}btn-secondary{% endif %}">
            Все
        </a>
        <a href="{{ url_for(request.endpoint, status='waiting') }}" class="btn {% if status == 'waiting' %}btn-primary{% else %}btn-secondary{% endif %}">
            Ожидают
        </a>
        <a href="{{ url_for(request.endpoint, status='in_progress') }}" class="btn {% if status == 'in_progress' %}btn-primary{% else %}btn-secondary{% endif %}">
            В процессе
        </a>
        <a href="{{ url_for(request.endpoint, status='completed') }}" class="btn {% if status == 'completed' %}btn-primary{% else %}btn-secondary{% endif %}">
            Завершены
        </a>
    </div>
</div>

<div class="task-list">
    {% for task in tasks %}
    <div class="task-card fade-in">
        <div class="task-header">
            <h2 class="task-title">{{ task.title }}</h2>
            <div class="task-meta">
                <span class="task-type {% if task.task_type == 'личная' %}type-personal{% else %}type-general{% endif %}">
                    {{ task.task_type }}
                </span>
            </div>
        </div>
        
        <p class="task-description">{{ task.description }}</p>
        
        <div class="task-meta">
            <span>Создал: {{ task.creator.name }}</span>
            {% if task.assignee %}
            <span>•</span>
            <span>Исполнитель: {{ task.assignee.name }}</span>
            {% endif %}
        </div>

        <div class="task-footer">
            <div class="task-status-group">
                <span class="task-status status-{{ task.status.replace(' ', '-') }}">
                    {{ task.status }}
                </span>
                
                <span class="task-deadline {% if task.is_overdue %}overdue{% endif %}">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" stroke="currentColor">
                        <circle cx="8" cy="8" r="7"/>
                        <path d="M8 4v4l3 3"/>
                    </svg>
                    {{ task.deadline.strftime('%d.%m.%Y') }}
                </span>
            </div>

            <div class="task-actions">
                {% if current_user.is_admin or task.creator_id == current_user.id %}
                <a href="{{ url_for('edit_task', task_id=task.id) }}" class="btn btn-secondary">
                    Редактировать
                </a>
                <form action="{{ url_for('delete_task', task_id=task.id) }}" method="post" class="d-inline" onsubmit="return confirm('Вы уверены?');">
                    <button type="submit" class="btn btn-secondary">Удалить</button>
                </form>
                {% endif %}
                
                {% if task.status == 'ждет принятия задачи' and task.assignee_id == current_user.id %}
                <form action="{{ url_for('update_task_status', task_id=task.id) }}" method="post" class="d-inline">
                    <input type="hidden" name="status" value="в процессе">
                    <button type="submit" class="btn btn-primary">Принять задачу</button>
                </form>
                {% endif %}
                
                {% if task.status == 'в процессе' and task.assignee_id == current_user.id %}
                <form action="{{ url_for('update_task_status', task_id=task.id) }}" method="post" class="d-inline">
                    <input type="hidden" name="status" value="выполнена">
                    <button type="submit" class="btn btn-primary">Завершить</button>
                </form>
                {% endif %}
            </div>
        </div>
    </div>
    {% else %}
    <div class="no-tasks fade-in">
        <p>Задач не найдено</p>
    </div>
    {% endfor %}
</div>

{% if pages > 1 %}
<div class="pagination">
    {% for page in range(1, pages + 1) %}
    <a href="{{ url_for(request.endpoint, page=page, status=status) }}" 
       class="page-item {% if page == current_page %}active{% endif %}">
        {{ page }}
    </a>
    {% endfor %}
</div>
{% endif %}
{% endblock %} 