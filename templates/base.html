<!DOCTYPE html>
<html lang="ru" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}ToDo App{% endblock %}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/variables.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/base.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/tasks.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <header class="header">
        <nav class="nav container">
            <div class="nav-brand">
                <a href="{{ url_for('index') }}" class="logo">ToDo App</a>
            </div>
            
            <div class="nav-links">
                {% if current_user.is_authenticated %}
                    {% if current_user.is_admin %}
                        <a href="{{ url_for('all_tasks') }}" class="nav-link">Все задачи</a>
                        <a href="{{ url_for('create_task') }}" class="btn btn-primary">Создать задачу</a>
                    {% else %}
                        <a href="{{ url_for('my_tasks') }}" class="nav-link">Мои задачи</a>
                        <a href="{{ url_for('create_personal_task') }}" class="btn btn-primary">Создать личную задачу</a>
                    {% endif %}
                    <button class="theme-switch" id="themeSwitch">
                        <svg class="theme-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="5"/>
                            <line x1="12" y1="1" x2="12" y2="3"/>
                            <line x1="12" y1="21" x2="12" y2="23"/>
                            <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"/>
                            <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"/>
                            <line x1="1" y1="12" x2="3" y2="12"/>
                            <line x1="21" y1="12" x2="23" y2="12"/>
                            <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"/>
                            <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"/>
                        </svg>
                    </button>
                    <a href="{{ url_for('logout') }}" class="btn btn-secondary">Выйти</a>
                {% else %}
                    <a href="{{ url_for('login') }}" class="btn btn-primary">Войти</a>
                    <a href="{{ url_for('register') }}" class="btn btn-secondary">Регистрация</a>
                {% endif %}
            </div>
        </nav>
    </header>

    <main class="container">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }} fade-in">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
        
        {% block content %}{% endblock %}
    </main>

    <script>
        // Theme switcher
        const themeSwitch = document.getElementById('themeSwitch');
        const html = document.documentElement;
        
        // Check for saved theme preference
        const savedTheme = localStorage.getItem('theme') || 'light';
        html.setAttribute('data-theme', savedTheme);
        
        themeSwitch.addEventListener('click', () => {
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        });

        // Auto-hide alerts after 5 seconds
        document.querySelectorAll('.alert').forEach(alert => {
            setTimeout(() => {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            }, 5000);
        });
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html> 