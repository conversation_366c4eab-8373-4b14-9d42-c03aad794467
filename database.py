from models import User, Task
from extensions import db
from datetime import datetime

def create_user(name: str, phone: str) -> User:
    is_admin = name.endswith('_admin')
    if is_admin:
        name = name[:-6]  # Убираем суффикс _admin
    
    user = User(name=name, phone=phone, is_admin=is_admin)
    db.session.add(user)
    db.session.commit()
    return user

def get_user_by_phone(phone: str) -> User:
    return User.query.filter_by(phone=phone).first()

def create_task(title, description, creator_id, assignee_id, deadline, task_type):
    task = Task(
        title=title,
        description=description,
        creator_id=creator_id,
        assignee_id=assignee_id,
        deadline=deadline,
        task_type=task_type
    )
    db.session.add(task)
    db.session.commit()
    return task

def get_user_tasks(user_id: int, page: int = 1, status=None):
    query = Task.query.filter(
        (Task.creator_id == user_id) | (Task.assignee_id == user_id)
    )
    
    if status:
        if status == 'waiting':
            query = query.filter_by(status='ждет принятия задачи')
        elif status == 'in_progress':
            query = query.filter_by(status='в процессе')
        elif status == 'completed':
            query = query.filter_by(status='выполнена')
    
    return query.order_by(Task.created_at.desc()).paginate(
        page=page, per_page=15, error_out=False
    )

def get_all_tasks(page: int = 1, status=None):
    query = Task.query
    
    if status:
        if status == 'waiting':
            query = query.filter_by(status='ждет принятия задачи')
        elif status == 'in_progress':
            query = query.filter_by(status='в процессе')
        elif status == 'completed':
            query = query.filter_by(status='выполнена')
    
    return query.order_by(Task.created_at.desc()).paginate(
        page=page, per_page=15, error_out=False
    )

def update_task_status(task_id: int, new_status: str) -> bool:
    task = Task.query.get(task_id)
    if task:
        task.status = new_status
        db.session.commit()
        return True
    return False

def delete_task(task_id: int) -> bool:
    task = Task.query.get(task_id)
    if task:
        db.session.delete(task)
        db.session.commit()
        return True
    return False 